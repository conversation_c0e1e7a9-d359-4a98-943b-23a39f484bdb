import { MediaData } from '@/types/media';
import { Ionicons } from '@expo/vector-icons';
import { useEvent } from 'expo';
import { Image } from 'expo-image';
import { useVideoPlayer, VideoView } from 'expo-video';
import React, { useCallback, useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Dimensions,
  Pressable,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface MediaCarouselItemProps {
  media: MediaData;
  isActive: boolean;
  onSingleTap: () => void;
  isHeaderVisible?: boolean; // Add prop to sync with header visibility
}

const MediaCarouselItem: React.FC<MediaCarouselItemProps> = ({
  media,
  isActive,
  onSingleTap,
  isHeaderVisible = true,
}) => {
  const [isLoading, setIsLoading] = useState(true);

  // Video Player Setup - Following Expo docs best practices
  const player = useVideoPlayer(media.type === 'video' ? media.url : null, (player) => {
    if (player) {
      player.loop = true;
      player.muted = true;
      player.timeUpdateEventInterval = 0.5; // Update every 500ms for smooth progress
    }
  });

  const { isPlaying } = useEvent(player, 'playingChange', {
    isPlaying: player.playing
  });

  const statusChangeEvent = useEvent(player, 'statusChange', {
    status: player.status,
    error: undefined
  });

  const status = statusChangeEvent?.status ?? player.status;
  const error = statusChangeEvent?.error ?? null;

  // Handle video autoplay when becoming active
  useEffect(() => {
    if (media.type === 'video' && player) {
      if (isActive) {
        player.play();
      } else {
        player.pause();
      }
    }
  }, [isActive, media.type, player]);

  // Handle player status changes for error handling
  useEffect(() => {
    if (status === 'error') {
      setIsLoading(false);
      console.error('Video player error:', error);
    } else if (status === 'readyToPlay') {
      setIsLoading(false);
    }
  }, [status, error]);

  // Event Handlers
  const handleLoad = useCallback(() => setIsLoading(false), []);

  const togglePlayPause = useCallback(() => {
    if (player) {
      if (isPlaying) {
        player.pause();
      } else {
        player.play();
      }
    }
  }, [player, isPlaying]);

  // Touch handlers for video interaction
  const handleVideoPress = useCallback(() => {
    if (media.type === 'image') {
      // For images, just toggle header
      onSingleTap();
    } else if (media.type === 'video') {
      // For videos, if playing and header is hidden, pause the video
      // Otherwise, toggle header visibility
      if (isPlaying && !isHeaderVisible) {
        togglePlayPause();
      } else {
        onSingleTap();
      }
    }
  }, [media.type, onSingleTap, isPlaying, isHeaderVisible, togglePlayPause]);

  // Render Functions
  const renderImage = () => (
    <Image
      source={{ uri: media.url }}
      style={styles.media}
      contentFit="contain"
      transition={300}
      onLoad={handleLoad}
      placeholder={{ blurhash: 'LGF5?+00J:0.~VxaJEtl0{s8NHj]' }}
    />
  );

  const renderVideo = () => (
    <View style={styles.videoContainer}>
      <VideoView
        player={player}
        style={styles.media}
        nativeControls={false}
        contentFit="contain"
        allowsFullscreen={false}
        allowsPictureInPicture={false}
        onFirstFrameRender={handleLoad}
      />

      {/* Play/Pause button in the center - always show when paused, show with header when playing */}
      {(!isPlaying || isHeaderVisible) && (
        <View style={styles.playPauseOverlay}>
          <TouchableOpacity
            style={[
              styles.playPauseButton,
              !isPlaying && styles.playButtonPaused // More prominent when paused
            ]}
            onPress={togglePlayPause}
            activeOpacity={0.8}
          >
            <Ionicons
              name={isPlaying ? "pause" : "play"}
              size={40}
              color="#fff"
            />
          </TouchableOpacity>
        </View>
      )}

      {status === 'error' && (
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={48} color="#fff" />
          <Text style={styles.errorText}>Failed to load video</Text>
        </View>
      )}
    </View>
  );

  return (
    <View style={styles.container}>
      <Pressable style={styles.gestureContainer} onPress={handleVideoPress}>
        {media.type === 'video' ? renderVideo() : renderImage()}
      </Pressable>

      {isLoading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#fff" />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent',
    alignItems: 'center',
    justifyContent: 'center',
  },
  gestureContainer: {
    width: SCREEN_WIDTH,
    height: SCREEN_HEIGHT,
  },
  media: {
    flex: 1,
  },
  videoContainer: {
    flex: 1,
  },
  playPauseOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    alignItems: 'center',
    justifyContent: 'center',
    pointerEvents: 'box-none', // Allow taps to pass through to parent for header toggle
  },
  playPauseButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 8,
    pointerEvents: 'auto', // Button itself should capture taps
  },
  playButtonPaused: {
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    shadowOpacity: 0.4,
    shadowRadius: 8,
  },
  loadingContainer: {
    ...StyleSheet.absoluteFillObject,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 10,
  },
  errorContainer: {
    ...StyleSheet.absoluteFillObject,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  errorText: {
    color: '#fff',
    fontSize: 16,
    marginTop: 8,
    textAlign: 'center',
  },
});

export default React.memo(MediaCarouselItem);